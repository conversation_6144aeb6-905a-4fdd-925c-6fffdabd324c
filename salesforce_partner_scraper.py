import time
import pandas as pd
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

def setup_driver():
    """Set up and return a Chrome webdriver with appropriate options."""
    chrome_options = Options()
    # Run in visible mode to see what's happening
    # chrome_options.add_argument("--headless")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()),
                             options=chrome_options)

    # Execute script to remove webdriver property
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

    return driver

def scrape_partner_list():
    """Scrape the main partner list page and collect partner details."""
    driver = setup_driver()
    partners_data = []

    try:
        # Navigate to the main page
        driver.get("https://findpartners.salesforce.com/")
        print("Navigated to Salesforce partner finder page")

        # Step 1: Handle cookie consent
        print("Looking for cookie consent dialog...")
        try:
            # Wait for cookie banner to appear and click accept
            cookie_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.ID, "onetrust-accept-btn-handler"))
            )
            cookie_button.click()
            print("Clicked cookie consent button")
            time.sleep(3)
        except Exception as e:
            print(f"Cookie consent not found or already accepted: {e}")

        # Step 2: Wait for the main content to load
        print("Waiting for main content to load...")
        try:
            # Wait for the main app container to be present
            WebDriverWait(driver, 30).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # Wait for JavaScript to load by checking for specific elements that indicate the app is ready
            print("Waiting for app to initialize...")
            max_wait_time = 60  # 60 seconds max wait
            start_time = time.time()

            while time.time() - start_time < max_wait_time:
                # Check if we can find any interactive elements that suggest the app has loaded
                interactive_elements = driver.find_elements(By.CSS_SELECTOR, "input, button, select")
                if len(interactive_elements) > 5:  # If we have several interactive elements, app likely loaded
                    print(f"Found {len(interactive_elements)} interactive elements, app seems loaded")
                    break

                # Also check for any elements with substantial text content
                text_elements = driver.find_elements(By.XPATH, "//*[string-length(text()) > 20]")
                if len(text_elements) > 10:
                    print(f"Found {len(text_elements)} text elements, content seems loaded")
                    break

                print("Still waiting for content to load...")
                time.sleep(3)

            # Additional wait to ensure everything is fully rendered
            time.sleep(5)

        except Exception as e:
            print(f"Error waiting for content: {e}")

        # Step 3: Look for and interact with location filter
        print("Looking for location filter...")
        try:
            # Try to find any input fields or dropdowns that might be for location
            all_inputs = driver.find_elements(By.TAG_NAME, "input")
            all_selects = driver.find_elements(By.TAG_NAME, "select")
            all_buttons = driver.find_elements(By.TAG_NAME, "button")

            print(f"Found {len(all_inputs)} inputs, {len(all_selects)} selects, {len(all_buttons)} buttons")

            # Look for elements that might contain "location", "country", "filter" text
            location_found = False

            # Check inputs for location-related placeholders or labels
            for input_elem in all_inputs:
                try:
                    placeholder = input_elem.get_attribute("placeholder") or ""
                    aria_label = input_elem.get_attribute("aria-label") or ""
                    name = input_elem.get_attribute("name") or ""

                    if any(keyword in (placeholder + aria_label + name).lower()
                           for keyword in ["location", "country", "region", "where"]):
                        print(f"Found location input: placeholder='{placeholder}', aria-label='{aria_label}', name='{name}'")
                        input_elem.clear()
                        input_elem.send_keys("France")
                        time.sleep(2)
                        # Try to trigger search by pressing Enter
                        from selenium.webdriver.common.keys import Keys
                        input_elem.send_keys(Keys.ENTER)
                        location_found = True
                        print("Entered France in location field")
                        break
                except Exception as e:
                    continue

            # If no input found, look for buttons that might open location filters
            if not location_found:
                for button in all_buttons:
                    try:
                        button_text = button.text.lower()
                        aria_label = (button.get_attribute("aria-label") or "").lower()

                        if any(keyword in (button_text + aria_label)
                               for keyword in ["location", "filter", "country", "where"]):
                            print(f"Found location button: text='{button.text}', aria-label='{button.get_attribute('aria-label')}'")
                            button.click()
                            time.sleep(3)

                            # After clicking, look for France option
                            france_elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'France')]")
                            if france_elements:
                                france_elements[0].click()
                                print("Clicked France option")
                                location_found = True
                                break
                    except Exception as e:
                        continue

            if location_found:
                print("Waiting for filtered results...")
                time.sleep(15)  # Wait longer for results to load
            else:
                print("No location filter found, proceeding with all results")

        except Exception as e:
            print(f"Error with location filtering: {e}")

        # Step 4: Look for partner results with more comprehensive approach
        print("Searching for partner results...")

        # Wait a bit more for dynamic content
        time.sleep(5)

        # Try to find any elements that might contain partner information
        # Look for common patterns in partner listing pages
        potential_selectors = [
            # Lightning Web Components
            "c-partner-card",
            "c-partner-tile",
            "c-search-result",
            "lightning-card",

            # Generic card patterns
            ".card",
            ".partner-card",
            ".result-card",
            ".search-result",
            ".partner-tile",
            ".listing-item",

            # Div patterns
            "div[class*='partner']",
            "div[class*='card']",
            "div[class*='result']",
            "div[class*='tile']",
            "div[class*='listing']",

            # Article/section patterns
            "article",
            "section[class*='partner']",
            "section[class*='result']",

            # List patterns
            "li[class*='partner']",
            "li[class*='result']",
            "ul > li",

            # Data attributes
            "[data-testid*='partner']",
            "[data-testid*='card']",
            "[data-testid*='result']"
        ]

        partner_elements = []
        for selector in potential_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    # Filter out elements that are too small (likely not partner cards)
                    valid_elements = []
                    for elem in elements:
                        try:
                            if elem.is_displayed() and elem.size['height'] > 50 and elem.size['width'] > 100:
                                text_content = elem.text.strip()
                                if len(text_content) > 20:  # Has substantial content
                                    valid_elements.append(elem)
                        except:
                            continue

                    if valid_elements:
                        print(f"Found {len(valid_elements)} valid elements with selector: {selector}")
                        partner_elements = valid_elements
                        break

            except Exception as e:
                continue

        print(f"Total partner elements found: {len(partner_elements)}")

        # If we found elements, try to extract basic information
        if partner_elements:
            print("Extracting partner information...")
            for i, elem in enumerate(partner_elements[:5]):  # Limit to first 5 for testing
                try:
                    partner_info = {
                        "name": "Unknown",
                        "text_content": elem.text[:200] if elem.text else "No text",
                        "element_tag": elem.tag_name,
                        "element_classes": elem.get_attribute("class") or ""
                    }

                    # Try to find partner name within the element
                    name_selectors = ["h1", "h2", "h3", "h4", ".name", ".title", ".partner-name"]
                    for name_sel in name_selectors:
                        try:
                            name_elem = elem.find_element(By.CSS_SELECTOR, name_sel)
                            if name_elem.text.strip():
                                partner_info["name"] = name_elem.text.strip()
                                break
                        except:
                            continue

                    partners_data.append(partner_info)
                    print(f"Partner {i+1}: {partner_info['name']}")

                except Exception as e:
                    print(f"Error extracting info from element {i}: {e}")
                    continue

        # Save final page source for analysis
        with open("page_source_final.html", "w", encoding="utf-8") as f:
            f.write(driver.page_source)
        print("Final page source saved to page_source_final.html")

        return partners_data

    finally:
        driver.quit()

def scrape_partner_details(driver):
    """Scrape detailed information from a partner's page."""
    # Wait for page to load
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, ".some-element-on-detail-page"))
    )
    
    partner_details = {}
    
    # 1. Get rating and review count
    try:
        rating = driver.find_element(By.CSS_SELECTOR, ".rating-element").text
        review_count = driver.find_element(By.CSS_SELECTOR, ".review-count-element").text
        partner_details["rating"] = rating
        partner_details["review_count"] = review_count
    except:
        partner_details["rating"] = "N/A"
        partner_details["review_count"] = "N/A"
    
    # 2. Get certified experts count
    # ...similar code for other data points...
    
    return partner_details

def main():
    partners_data = scrape_partner_list()
    
    # Convert to DataFrame and save to CSV
    df = pd.DataFrame(partners_data)
    df.to_csv("salesforce_partners_data.csv", index=False)
    print(f"Scraped data for {len(partners_data)} partners and saved to CSV.")

if __name__ == "__main__":
    main()