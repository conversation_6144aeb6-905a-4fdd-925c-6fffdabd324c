import time
import pandas as pd
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

def setup_driver():
    """Set up and return a Chrome webdriver with appropriate options."""
    chrome_options = Options()
    # Run in visible mode to see what's happening
    # chrome_options.add_argument("--headless")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()),
                             options=chrome_options)

    # Execute script to remove webdriver property
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

    return driver

def scrape_partner_list():
    """Scrape the main partner list page and collect partner details."""
    driver = setup_driver()
    partners_data = []

    try:
        # Navigate to the main page
        driver.get("https://findpartners.salesforce.com/")
        print("Navigated to Salesforce partner finder page")

        # Step 1: Handle cookie consent
        print("Looking for cookie consent dialog...")
        try:
            # Wait for cookie banner to appear and click accept
            cookie_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.ID, "onetrust-accept-btn-handler"))
            )
            cookie_button.click()
            print("Clicked cookie consent button")
            time.sleep(3)
        except Exception as e:
            print(f"Cookie consent not found or already accepted: {e}")

        # Step 2: Wait for the main content to load with more patience
        print("Waiting for main content to load...")
        try:
            # Wait for the main app container to be present
            WebDriverWait(driver, 30).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # Wait for JavaScript to load by checking for specific elements that indicate the app is ready
            print("Waiting for app to initialize...")
            max_wait_time = 120  # 2 minutes max wait
            start_time = time.time()

            while time.time() - start_time < max_wait_time:
                # Check if we can find the specific input-27 field
                try:
                    input_27 = driver.find_element(By.ID, "input-27")
                    print("✅ Found input-27 field! App is ready.")
                    break
                except:
                    pass

                # Check if we can find any interactive elements that suggest the app has loaded
                interactive_elements = driver.find_elements(By.CSS_SELECTOR, "input, button, select")
                if len(interactive_elements) > 10:  # Wait for more elements to ensure full load
                    print(f"Found {len(interactive_elements)} interactive elements")

                    # Also check for Salesforce-specific elements
                    sf_elements = driver.find_elements(By.CSS_SELECTOR, "[class*='slds'], [class*='pf-'], lightning-*")
                    if len(sf_elements) > 5:
                        print(f"Found {len(sf_elements)} Salesforce Lightning elements, app likely loaded")
                        break

                print("Still waiting for content to load...")
                time.sleep(5)

            # Additional wait to ensure everything is fully rendered
            print("Giving extra time for dynamic content...")
            time.sleep(10)

        except Exception as e:
            print(f"Error waiting for content: {e}")

        # Step 3: Look for and interact with location filter
        print("Looking for location filter accordion and input...")
        try:
            location_found = False

            # First, try to find and open the Location accordion/filter section
            print("Looking for the Location filter accordion button...")
            accordion_opened = False

            try:
                # Look for the specific Location filter button based on provided info
                location_button_selectors = [
                    "button.pf-filter_accordion-icon[aria-label='filterButton'][aria-controls*='location']",
                    "button[class*='pf-filter_accordion-icon'][aria-label='filterButton']",
                    "button[aria-controls*='location']",
                    "button[aria-label='filterButton']"
                ]

                location_button = None
                for selector in location_button_selectors:
                    try:
                        buttons = driver.find_elements(By.CSS_SELECTOR, selector)
                        for button in buttons:
                            aria_controls = button.get_attribute("aria-controls") or ""
                            if "location" in aria_controls.lower():
                                location_button = button
                                print(f"Found Location filter button with selector: {selector}")
                                print(f"aria-controls: {aria_controls}")
                                break
                        if location_button:
                            break
                    except Exception as e:
                        continue

                if location_button:
                    # Scroll to the button to make it visible
                    driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", location_button)
                    time.sleep(2)

                    # Check if it's already expanded
                    aria_expanded = location_button.get_attribute("aria-expanded")
                    print(f"Location filter aria-expanded: {aria_expanded}")

                    if aria_expanded != "true":
                        # Try clicking to expand the location filter
                        try:
                            location_button.click()
                            print("Clicked Location filter button normally")
                        except Exception as e:
                            print(f"Normal click failed: {e}, trying JavaScript click")
                            driver.execute_script("arguments[0].click();", location_button)
                            print("Clicked Location filter button with JavaScript")

                        time.sleep(3)  # Wait for accordion to expand
                    else:
                        print("Location filter is already expanded")

                    accordion_opened = True
                else:
                    print("Could not find Location filter button")

            except Exception as e:
                print(f"Error finding Location filter button: {e}")

            if accordion_opened:
                print("Location filter panel should now be open")
            else:
                print("Could not open Location filter panel")

            # Now try to find the specific input field input-27
            print("Looking for input-27 field (Country name input)...")
            input_found = False

            # Wait a bit more for the page content to fully load
            time.sleep(5)

            # Try multiple approaches to find and interact with input-27
            for attempt in range(10):
                try:
                    print(f"Attempt {attempt + 1}: Looking for input-27...")

                    # Try different selectors for the input field
                    input_selectors = [
                        "#input-27",
                        "input[id='input-27']",
                        "input[placeholder='Country name']",
                        "input.slds-input[placeholder='Country name']",
                        "input[type='search'][placeholder='Country name']"
                    ]

                    location_input = None
                    for selector in input_selectors:
                        try:
                            location_input = driver.find_element(By.CSS_SELECTOR, selector)
                            print(f"Found input with selector: {selector}")
                            break
                        except:
                            continue

                    if location_input:
                        # Check if the input is visible and interactable
                        is_displayed = location_input.is_displayed()
                        is_enabled = location_input.is_enabled()
                        print(f"Input visibility: displayed={is_displayed}, enabled={is_enabled}")

                        if not is_displayed:
                            print("Input is not visible, trying to make it visible...")
                            # Try to scroll to it and make it visible
                            driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", location_input)
                            time.sleep(2)

                            # Try to click any parent elements that might expand the section
                            parent = location_input.find_element(By.XPATH, "..")
                            try:
                                parent.click()
                                time.sleep(2)
                            except:
                                pass

                        # Try to interact with the input
                        try:
                            # Use JavaScript to make sure it's visible and focused
                            driver.execute_script("arguments[0].style.display = 'block';", location_input)
                            driver.execute_script("arguments[0].style.visibility = 'visible';", location_input)
                            driver.execute_script("arguments[0].focus();", location_input)

                            # Clear and enter France
                            location_input.clear()
                            location_input.send_keys("France")
                            print("✅ Successfully entered 'France' in input-27!")

                            # Try to trigger the search/filter
                            from selenium.webdriver.common.keys import Keys
                            location_input.send_keys(Keys.ENTER)
                            time.sleep(3)

                            # Also try clicking any nearby search/apply button
                            try:
                                search_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Search') or contains(text(), 'Apply') or contains(text(), 'Filter')]")
                                if search_buttons:
                                    search_buttons[0].click()
                                    print("Clicked search/apply button")
                            except:
                                pass

                            location_found = True
                            input_found = True
                            print("✅ Successfully set location filter to France")
                            break

                        except Exception as e:
                            print(f"Could not interact with input: {e}")
                    else:
                        print("Could not find input-27 with any selector")

                except Exception as e:
                    print(f"Attempt {attempt + 1} failed: {e}")

                # Wait before next attempt
                time.sleep(3)

            if not input_found:
                print("Could not find input-27 after multiple attempts")

                # Fallback: try to find any input that might be for location
                print("Trying fallback approach to find location input...")
                all_inputs = driver.find_elements(By.TAG_NAME, "input")
                print(f"Found {len(all_inputs)} input elements total")

                for i, input_elem in enumerate(all_inputs):
                    try:
                        input_id = input_elem.get_attribute("id") or ""
                        placeholder = input_elem.get_attribute("placeholder") or ""
                        aria_label = input_elem.get_attribute("aria-label") or ""
                        input_type = input_elem.get_attribute("type") or ""

                        print(f"Input {i}: id='{input_id}', placeholder='{placeholder}', type='{input_type}'")

                        if any(keyword in (input_id + placeholder + aria_label).lower()
                               for keyword in ["location", "country", "region", "where", "place"]):
                            print(f"Found potential location input: id='{input_id}', placeholder='{placeholder}'")
                            input_elem.clear()
                            input_elem.send_keys("France")
                            input_elem.send_keys(Keys.ENTER)
                            location_found = True
                            print("Entered France in fallback location field")
                            break
                    except Exception as e:
                        continue

            if location_found:
                print("Waiting for filtered results to load...")
                time.sleep(15)  # Wait longer for results to load after filtering
            else:
                print("Could not set location filter, proceeding with all results")

        except Exception as e:
            print(f"Error with location filtering: {e}")

        # Step 4: Look for partner results with more comprehensive approach
        print("Searching for partner results...")

        # Wait a bit more for dynamic content
        time.sleep(5)

        # Try to find any elements that might contain partner information
        # Look for common patterns in partner listing pages
        potential_selectors = [
            # Lightning Web Components
            "c-partner-card",
            "c-partner-tile",
            "c-search-result",
            "lightning-card",

            # Generic card patterns
            ".card",
            ".partner-card",
            ".result-card",
            ".search-result",
            ".partner-tile",
            ".listing-item",

            # Div patterns
            "div[class*='partner']",
            "div[class*='card']",
            "div[class*='result']",
            "div[class*='tile']",
            "div[class*='listing']",

            # Article/section patterns
            "article",
            "section[class*='partner']",
            "section[class*='result']",

            # List patterns
            "li[class*='partner']",
            "li[class*='result']",
            "ul > li",

            # Data attributes
            "[data-testid*='partner']",
            "[data-testid*='card']",
            "[data-testid*='result']"
        ]

        partner_elements = []
        for selector in potential_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    # Filter out elements that are too small (likely not partner cards)
                    valid_elements = []
                    for elem in elements:
                        try:
                            if elem.is_displayed() and elem.size['height'] > 50 and elem.size['width'] > 100:
                                text_content = elem.text.strip()
                                if len(text_content) > 20:  # Has substantial content
                                    valid_elements.append(elem)
                        except:
                            continue

                    if valid_elements:
                        print(f"Found {len(valid_elements)} valid elements with selector: {selector}")
                        partner_elements = valid_elements
                        break

            except Exception as e:
                continue

        print(f"Total partner elements found: {len(partner_elements)}")

        # If we found elements, try to extract basic information
        if partner_elements:
            print("Extracting partner information...")
            for i, elem in enumerate(partner_elements[:5]):  # Limit to first 5 for testing
                try:
                    partner_info = {
                        "name": "Unknown",
                        "text_content": elem.text[:200] if elem.text else "No text",
                        "element_tag": elem.tag_name,
                        "element_classes": elem.get_attribute("class") or ""
                    }

                    # Try to find partner name within the element
                    name_selectors = ["h1", "h2", "h3", "h4", ".name", ".title", ".partner-name"]
                    for name_sel in name_selectors:
                        try:
                            name_elem = elem.find_element(By.CSS_SELECTOR, name_sel)
                            if name_elem.text.strip():
                                partner_info["name"] = name_elem.text.strip()
                                break
                        except:
                            continue

                    partners_data.append(partner_info)
                    print(f"Partner {i+1}: {partner_info['name']}")

                except Exception as e:
                    print(f"Error extracting info from element {i}: {e}")
                    continue

        # Save final page source for analysis
        with open("page_source_final.html", "w", encoding="utf-8") as f:
            f.write(driver.page_source)
        print("Final page source saved to page_source_final.html")

        # Take a screenshot to see what the page looks like
        try:
            driver.save_screenshot("page_screenshot.png")
            print("Screenshot saved to page_screenshot.png")
        except Exception as e:
            print(f"Could not save screenshot: {e}")

        return partners_data

    finally:
        driver.quit()

def scrape_partner_details(driver):
    """Scrape detailed information from a partner's page."""
    # Wait for page to load
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, ".some-element-on-detail-page"))
    )
    
    partner_details = {}
    
    # 1. Get rating and review count
    try:
        rating = driver.find_element(By.CSS_SELECTOR, ".rating-element").text
        review_count = driver.find_element(By.CSS_SELECTOR, ".review-count-element").text
        partner_details["rating"] = rating
        partner_details["review_count"] = review_count
    except:
        partner_details["rating"] = "N/A"
        partner_details["review_count"] = "N/A"
    
    # 2. Get certified experts count
    # ...similar code for other data points...
    
    return partner_details

def main():
    partners_data = scrape_partner_list()

    # Convert to DataFrame and save to CSV
    df = pd.DataFrame(partners_data)
    df.to_csv("salesforce_partners_data.csv", index=False)
    print(f"Scraped data for {len(partners_data)} partners and saved to CSV.")

    if len(partners_data) == 0:
        print("\n" + "="*50)
        print("TROUBLESHOOTING SUMMARY")
        print("="*50)
        print("❌ No partners found. This could be due to:")
        print("1. The location filter (input-27) is not accessible")
        print("2. The accordion/filter section needs different interaction")
        print("3. The page structure has changed")
        print("4. Additional wait time needed for dynamic content")
        print("\nNext steps:")
        print("- Check page_screenshot.png to see the actual page state")
        print("- Review page_source_final.html for the actual HTML structure")
        print("- The filter button was found but input-27 was not accessible")
        print("="*50)

if __name__ == "__main__":
    main()