import time
import pandas as pd
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

def setup_driver():
    """Set up and return a Chrome webdriver with appropriate options."""
    chrome_options = Options()
    # Run in visible mode to see what's happening
    # chrome_options.add_argument("--headless")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()),
                             options=chrome_options)

    # Execute script to remove webdriver property
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

    return driver

def scrape_partner_list():
    """Scrape the main partner list page and collect partner details."""
    driver = setup_driver()
    partners_data = []

    try:
        # Navigate to the main page
        driver.get("https://findpartners.salesforce.com/")
        print("✅ Navigated to Salesforce partner finder page")

        # Step 1: Handle cookie consent
        print("Step 1: Handling cookie consent...")
        try:
            cookie_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.ID, "onetrust-accept-btn-handler"))
            )
            cookie_button.click()
            print("✅ Clicked cookie consent button")
            time.sleep(3)
        except Exception as e:
            print(f"⚠️ Cookie consent not found: {e}")

        # Step 2: Wait for page to load completely with patience
        print("Step 2: Waiting for page to load completely...")

        # Wait for Salesforce Lightning components to load
        max_wait = 60  # 1 minute
        start_time = time.time()

        while time.time() - start_time < max_wait:
            try:
                # Check for Salesforce Lightning elements
                lightning_elements = driver.find_elements(By.CSS_SELECTOR, "[class*='slds'], [class*='pf-']")
                buttons = driver.find_elements(By.TAG_NAME, "button")

                print(f"Found {len(lightning_elements)} Lightning elements, {len(buttons)} buttons")

                if len(lightning_elements) > 20 and len(buttons) > 10:
                    print("✅ Page appears to be fully loaded")
                    break

            except Exception as e:
                print(f"Error checking page load: {e}")

            print("Still waiting for page to load...")
            time.sleep(5)

        # Additional wait for dynamic content
        time.sleep(10)

        # Step 3: Find Location accordion button with comprehensive search
        print("Step 3: Comprehensive search for Location accordion button...")
        location_button = None

        # Search through all buttons multiple times as they may load dynamically
        for search_attempt in range(3):
            print(f"Search attempt {search_attempt + 1}...")

            all_buttons = driver.find_elements(By.TAG_NAME, "button")
            print(f"Found {len(all_buttons)} total buttons")

            for i, button in enumerate(all_buttons):
                try:
                    aria_controls = button.get_attribute("aria-controls") or ""
                    aria_label = button.get_attribute("aria-label") or ""
                    classes = button.get_attribute("class") or ""
                    aria_expanded = button.get_attribute("aria-expanded") or ""
                    button_text = button.text.strip()

                    # Debug: Print details of buttons that might be relevant
                    if any(keyword in (aria_controls + aria_label + classes + button_text).lower()
                           for keyword in ["location", "filter", "accordion"]):
                        print(f"  Button {i}: text='{button_text}', aria-controls='{aria_controls}', aria-label='{aria_label}', classes='{classes[:50]}...'")

                    # Look for the specific Location accordion button with multiple patterns
                    if (("location" in aria_controls.lower() and ("19" in aria_controls or "Q" in aria_controls)) or
                        ("filterButton" in aria_label and "pf-filter_accordion-icon" in classes) or
                        ("location" in aria_controls.lower() and "filterButton" in aria_label)):
                        location_button = button
                        print(f"✅ Found Location accordion button at index {i}")
                        print(f"  aria-controls: {aria_controls}")
                        print(f"  aria-expanded: {aria_expanded}")
                        print(f"  classes: {classes}")
                        break
                except Exception as e:
                    continue

            if location_button:
                break
            else:
                print(f"Location button not found in attempt {search_attempt + 1}, waiting...")
                time.sleep(5)

        # Step 4: Click the accordion button to open it
        accordion_opened = False
        if location_button:
            print("Step 4: Opening Location accordion...")
            try:
                # Scroll to button and click
                driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", location_button)
                time.sleep(2)

                aria_expanded = location_button.get_attribute("aria-expanded")
                if aria_expanded == "false":
                    location_button.click()
                    print("✅ Clicked Location accordion button")
                    time.sleep(5)  # Wait for accordion to expand
                    accordion_opened = True
                else:
                    print("✅ Location accordion already open")
                    accordion_opened = True

            except Exception as e:
                print(f"❌ Error clicking accordion: {e}")
        else:
            print("❌ Could not find Location accordion button")

        # Step 5: Find input-27 and enter France
        print("Step 5: Looking for input-27 field...")
        location_found = False

        if accordion_opened:
            time.sleep(3)  # Wait for accordion content to load

            # Try to find input-27
            for attempt in range(5):
                try:
                    location_input = driver.find_element(By.ID, "input-27")
                    print(f"✅ Found input-27 on attempt {attempt + 1}")

                    # Clear and enter France
                    location_input.clear()
                    location_input.send_keys("France")
                    print("✅ Entered 'France' in input-27")

                    # Press Enter to trigger search
                    from selenium.webdriver.common.keys import Keys
                    location_input.send_keys(Keys.ENTER)
                    time.sleep(3)

                    location_found = True
                    print("✅ Successfully set location filter to France")
                    break

                except Exception as e:
                    print(f"Attempt {attempt + 1}: Could not find input-27: {e}")
                    time.sleep(2)

        # Step 6: Wait for results to load
        if location_found:
            print("Step 6: Waiting for filtered results...")
            time.sleep(15)
        else:
            print("⚠️ Proceeding without location filter...")
            time.sleep(5)

        # Step 7: Look for partner results
        print("Step 7: Searching for partner results...")

        # Try common selectors for partner cards
        partner_selectors = [
            "c-partner-card",
            ".partner-card",
            ".card",
            "div[class*='partner']",
            "div[class*='card']",
            "article",
            "li[class*='partner']"
        ]

        partner_elements = []
        for selector in partner_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    # Filter for visible elements with content
                    valid_elements = [elem for elem in elements
                                    if elem.is_displayed() and len(elem.text.strip()) > 20]
                    if valid_elements:
                        print(f"✅ Found {len(valid_elements)} partners with selector: {selector}")
                        partner_elements = valid_elements
                        break
            except:
                continue

        # Step 8: Extract partner information
        if partner_elements:
            print("Step 8: Extracting partner information...")
            for i, elem in enumerate(partner_elements[:10]):  # Limit to first 10
                try:
                    partner_info = {
                        "name": "Unknown",
                        "description": elem.text[:300] if elem.text else "No description"
                    }

                    # Try to find partner name
                    for tag in ["h1", "h2", "h3", "h4"]:
                        try:
                            name_elem = elem.find_element(By.TAG_NAME, tag)
                            if name_elem.text.strip():
                                partner_info["name"] = name_elem.text.strip()
                                break
                        except:
                            continue

                    partners_data.append(partner_info)
                    print(f"Partner {i+1}: {partner_info['name']}")

                except Exception as e:
                    print(f"Error extracting partner {i}: {e}")

        print(f"✅ Found {len(partners_data)} partners total")

        # Save debug files
        try:
            driver.save_screenshot("page_screenshot.png")
            with open("page_source_final.html", "w", encoding="utf-8") as f:
                f.write(driver.page_source)
            print("✅ Saved screenshot and page source for debugging")
        except Exception as e:
            print(f"Could not save debug files: {e}")

        return partners_data

    finally:
        driver.quit()

def scrape_partner_details(driver):
    """Scrape detailed information from a partner's page."""
    # Wait for page to load
    WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, ".some-element-on-detail-page"))
    )
    
    partner_details = {}
    
    # 1. Get rating and review count
    try:
        rating = driver.find_element(By.CSS_SELECTOR, ".rating-element").text
        review_count = driver.find_element(By.CSS_SELECTOR, ".review-count-element").text
        partner_details["rating"] = rating
        partner_details["review_count"] = review_count
    except:
        partner_details["rating"] = "N/A"
        partner_details["review_count"] = "N/A"
    
    # 2. Get certified experts count
    # ...similar code for other data points...
    
    return partner_details

def main():
    partners_data = scrape_partner_list()

    # Convert to DataFrame and save to CSV
    df = pd.DataFrame(partners_data)
    df.to_csv("salesforce_partners_data.csv", index=False)
    print(f"Scraped data for {len(partners_data)} partners and saved to CSV.")

    if len(partners_data) == 0:
        print("\n" + "="*50)
        print("TROUBLESHOOTING SUMMARY")
        print("="*50)
        print("❌ No partners found. This could be due to:")
        print("1. The location filter (input-27) is not accessible")
        print("2. The accordion/filter section needs different interaction")
        print("3. The page structure has changed")
        print("4. Additional wait time needed for dynamic content")
        print("\nNext steps:")
        print("- Check page_screenshot.png to see the actual page state")
        print("- Review page_source_final.html for the actual HTML structure")
        print("- The filter button was found but input-27 was not accessible")
        print("="*50)

if __name__ == "__main__":
    main()