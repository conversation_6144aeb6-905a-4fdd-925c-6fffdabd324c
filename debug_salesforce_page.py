import time
import pandas as pd
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

def setup_driver():
    """Set up and return a Chrome webdriver with appropriate options."""
    chrome_options = Options()
    # Run in visible mode to see what's happening
    # chrome_options.add_argument("--headless")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    
    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), 
                             options=chrome_options)
    
    # Execute script to remove webdriver property
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    return driver

def debug_page_structure():
    """Debug the page structure to understand how to access the location filter."""
    driver = setup_driver()
    
    try:
        # Navigate to the main page
        driver.get("https://findpartners.salesforce.com/")
        print("Navigated to Salesforce partner finder page")
        
        # Handle cookie consent
        try:
            cookie_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.ID, "onetrust-accept-btn-handler"))
            )
            cookie_button.click()
            print("Clicked cookie consent button")
            time.sleep(3)
        except Exception as e:
            print(f"Cookie consent not found: {e}")
        
        # Wait for page to load
        time.sleep(10)
        
        print("\n" + "="*50)
        print("DEBUGGING PAGE STRUCTURE")
        print("="*50)
        
        # 1. Find all buttons and their properties
        print("\n1. ALL BUTTONS ON PAGE:")
        all_buttons = driver.find_elements(By.TAG_NAME, "button")
        for i, button in enumerate(all_buttons):
            try:
                text = button.text.strip()
                aria_label = button.get_attribute("aria-label") or ""
                button_id = button.get_attribute("id") or ""
                classes = button.get_attribute("class") or ""
                
                if text or aria_label or "filter" in classes.lower():
                    print(f"Button {i}: text='{text}', aria-label='{aria_label}', id='{button_id}', classes='{classes[:50]}...'")
            except:
                continue
        
        # 2. Find all inputs and their properties
        print("\n2. ALL INPUT FIELDS ON PAGE:")
        all_inputs = driver.find_elements(By.TAG_NAME, "input")
        for i, input_elem in enumerate(all_inputs):
            try:
                input_id = input_elem.get_attribute("id") or ""
                placeholder = input_elem.get_attribute("placeholder") or ""
                input_type = input_elem.get_attribute("type") or ""
                aria_label = input_elem.get_attribute("aria-label") or ""
                
                print(f"Input {i}: id='{input_id}', placeholder='{placeholder}', type='{input_type}', aria-label='{aria_label}'")
            except:
                continue
        
        # 3. Try clicking each button that might be a filter
        print("\n3. TRYING TO CLICK FILTER BUTTONS:")
        filter_buttons = []
        for i, button in enumerate(all_buttons):
            try:
                text = button.text.strip().lower()
                aria_label = (button.get_attribute("aria-label") or "").lower()
                classes = (button.get_attribute("class") or "").lower()
                
                if any(keyword in (text + aria_label + classes) for keyword in ["filter", "search", "location"]):
                    filter_buttons.append((i, button))
            except:
                continue
        
        for i, (button_index, button) in enumerate(filter_buttons):
            try:
                print(f"Clicking filter button {button_index}...")
                button.click()
                time.sleep(3)
                
                # After clicking, check for new inputs
                new_inputs = driver.find_elements(By.TAG_NAME, "input")
                print(f"After clicking button {button_index}, found {len(new_inputs)} inputs")
                
                # Look specifically for input-27 or location-related inputs
                for input_elem in new_inputs:
                    input_id = input_elem.get_attribute("id") or ""
                    placeholder = input_elem.get_attribute("placeholder") or ""
                    
                    if "27" in input_id or any(keyword in (input_id + placeholder).lower() 
                                              for keyword in ["location", "country", "where"]):
                        print(f"*** FOUND POTENTIAL LOCATION INPUT: id='{input_id}', placeholder='{placeholder}'")
                
                # Take a screenshot after each click
                driver.save_screenshot(f"debug_after_button_{button_index}.png")
                print(f"Screenshot saved: debug_after_button_{button_index}.png")
                
            except Exception as e:
                print(f"Error clicking button {button_index}: {e}")
        
        # 4. Try to find any elements with "input-27" in various ways
        print("\n4. SEARCHING FOR INPUT-27 WITH DIFFERENT METHODS:")
        selectors_to_try = [
            "input[id='input-27']",
            "#input-27",
            "input[id*='27']",
            "input[id*='input']",
            "*[id='input-27']",
            "*[id*='27']"
        ]
        
        for selector in selectors_to_try:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    print(f"Found {len(elements)} elements with selector: {selector}")
                    for elem in elements:
                        print(f"  - Tag: {elem.tag_name}, ID: {elem.get_attribute('id')}")
                else:
                    print(f"No elements found with selector: {selector}")
            except Exception as e:
                print(f"Error with selector {selector}: {e}")
        
        # 5. Save final state
        driver.save_screenshot("debug_final_state.png")
        with open("debug_page_source.html", "w", encoding="utf-8") as f:
            f.write(driver.page_source)
        
        print("\n" + "="*50)
        print("DEBUG COMPLETE")
        print("Files saved:")
        print("- debug_final_state.png")
        print("- debug_page_source.html")
        print("- debug_after_button_X.png (for each filter button clicked)")
        print("="*50)
        
    finally:
        input("Press Enter to close the browser...")
        driver.quit()

if __name__ == "__main__":
    debug_page_structure()
