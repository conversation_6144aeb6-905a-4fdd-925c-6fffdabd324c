import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

def setup_driver():
    """Set up and return a Chrome webdriver with appropriate options."""
    chrome_options = Options()
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    
    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), 
                             options=chrome_options)
    
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    return driver

def find_location_button():
    """Find the exact Location filter button on the page."""
    driver = setup_driver()
    
    try:
        # Navigate and handle cookies
        driver.get("https://findpartners.salesforce.com/")
        print("Navigated to Salesforce partner finder page")
        
        try:
            cookie_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.ID, "onetrust-accept-btn-handler"))
            )
            cookie_button.click()
            print("Clicked cookie consent button")
            time.sleep(3)
        except:
            print("Cookie consent not found")
        
        # Wait for page to load
        time.sleep(15)
        
        print("\n" + "="*60)
        print("SEARCHING FOR LOCATION FILTER BUTTON")
        print("="*60)
        
        # Search for buttons with specific patterns
        all_buttons = driver.find_elements(By.TAG_NAME, "button")
        print(f"Total buttons found: {len(all_buttons)}")
        
        location_related_buttons = []
        
        for i, button in enumerate(all_buttons):
            try:
                classes = button.get_attribute("class") or ""
                aria_label = button.get_attribute("aria-label") or ""
                aria_controls = button.get_attribute("aria-controls") or ""
                aria_expanded = button.get_attribute("aria-expanded") or ""
                text = button.text.strip()
                
                # Check if this button might be related to location/filter
                is_location_related = (
                    "location" in (classes + aria_label + aria_controls + text).lower() or
                    "filter" in (classes + aria_label + aria_controls + text).lower() or
                    "pf-filter" in classes or
                    "accordion" in classes or
                    aria_controls.startswith("location") or
                    "filterButton" in aria_label
                )
                
                if is_location_related:
                    location_related_buttons.append({
                        'index': i,
                        'button': button,
                        'classes': classes,
                        'aria_label': aria_label,
                        'aria_controls': aria_controls,
                        'aria_expanded': aria_expanded,
                        'text': text
                    })
                    
                    print(f"\nButton {i} (LOCATION RELATED):")
                    print(f"  Classes: {classes}")
                    print(f"  Aria-label: {aria_label}")
                    print(f"  Aria-controls: {aria_controls}")
                    print(f"  Aria-expanded: {aria_expanded}")
                    print(f"  Text: '{text}'")
                    
            except Exception as e:
                continue
        
        print(f"\nFound {len(location_related_buttons)} location-related buttons")
        
        # Try clicking each location-related button
        for btn_info in location_related_buttons:
            try:
                print(f"\n--- Trying to click button {btn_info['index']} ---")
                button = btn_info['button']
                
                # Scroll to button
                driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", button)
                time.sleep(2)
                
                # Try clicking
                try:
                    button.click()
                    print("✅ Clicked successfully with normal click")
                except:
                    driver.execute_script("arguments[0].click();", button)
                    print("✅ Clicked successfully with JavaScript")
                
                time.sleep(3)
                
                # After clicking, look for input-27 or location inputs
                print("Looking for input fields after click...")
                inputs = driver.find_elements(By.TAG_NAME, "input")
                
                for input_elem in inputs:
                    input_id = input_elem.get_attribute("id") or ""
                    placeholder = input_elem.get_attribute("placeholder") or ""
                    input_type = input_elem.get_attribute("type") or ""
                    
                    if ("27" in input_id or 
                        any(keyword in (input_id + placeholder).lower() 
                            for keyword in ["location", "country", "where", "place"])):
                        print(f"🎯 FOUND TARGET INPUT: id='{input_id}', placeholder='{placeholder}', type='{input_type}'")
                        
                        # Try entering France
                        try:
                            input_elem.clear()
                            input_elem.send_keys("France")
                            print("✅ Successfully entered 'France'")
                            
                            # Take screenshot of success
                            driver.save_screenshot(f"success_france_entered_{btn_info['index']}.png")
                            
                            return True
                        except Exception as e:
                            print(f"❌ Could not enter France: {e}")
                
                # Take screenshot after each button click
                driver.save_screenshot(f"after_button_click_{btn_info['index']}.png")
                
            except Exception as e:
                print(f"❌ Could not click button {btn_info['index']}: {e}")
        
        print("\n" + "="*60)
        print("SEARCH COMPLETE - No working location filter found")
        print("="*60)
        
        return False
        
    finally:
        input("Press Enter to close browser...")
        driver.quit()

if __name__ == "__main__":
    success = find_location_button()
    if success:
        print("🎉 Successfully found and used location filter!")
    else:
        print("❌ Could not find working location filter")
